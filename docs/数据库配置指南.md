# 数据库配置指南

## 概述

本项目支持两种数据库：
- **Supabase PostgreSQL**（默认，推荐用于生产环境）
- **MySQL**（用于特定需求或本地开发）

## 快速切换

通过设置环境变量 `DB_TYPE` 来切换数据库类型：

```bash
# 使用 Supabase（默认）
export DB_TYPE=supabase

# 使用 MySQL
export DB_TYPE=mysql
```

## Supabase 配置

1. 在 [Supabase Dashboard](https://supabase.com/dashboard) 创建项目
2. 获取数据库连接信息
3. 配置环境变量：

```bash
DB_TYPE=supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres.your-project-ref
SUPABASE_DB_PASSWORD=your-database-password
SUPABASE_DB_HOST=db.your-project-ref.supabase.co
SUPABASE_DB_PORT=5432
```

## MySQL 配置

1. 安装 MySQL 服务器
2. 创建数据库和用户
3. 配置环境变量：

```bash
DB_TYPE=mysql
MYSQL_DB_NAME=fyyd_django
MYSQL_DB_USER=your-mysql-user
MYSQL_DB_PASSWORD=your-mysql-password
MYSQL_DB_HOST=localhost
MYSQL_DB_PORT=3306
```

## 数据迁移

### 初始化数据库

```bash
# 创建迁移文件
python manage.py makemigrations

# 执行迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
```

### 切换数据库时的数据迁移

1. **导出当前数据库数据**：
   ```bash
   python manage.py dumpdata > data_backup.json
   ```

2. **切换数据库类型**：
   ```bash
   export DB_TYPE=mysql  # 或 supabase
   ```

3. **执行迁移**：
   ```bash
   python manage.py migrate
   ```

4. **导入数据**：
   ```bash
   python manage.py loaddata data_backup.json
   ```

## 注意事项

1. **SSL 连接**：Supabase 要求 SSL 连接，MySQL 配置中已禁用
2. **字符集**：MySQL 使用 utf8mb4 字符集以支持 emoji 等特殊字符
3. **时区设置**：项目设置为北京时间，数据库连接会自动处理时区转换
4. **依赖管理**：项目已包含两种数据库的驱动依赖

## 环境变量文件

复制 `.env.example` 到 `.env` 并根据你的环境配置相应的变量：

```bash
cp .env.example .env
# 编辑 .env 文件，填入你的数据库配置
```

## 验证连接

运行以下命令验证数据库连接：

```bash
python manage.py check --database default
```

## 常见问题

1. **PostgreSQL SSL 错误**：确保 `sslmode=require` 配置正确
2. **MySQL 连接错误**：检查 MySQL 服务是否启动，用户权限是否正确
3. **字符编码问题**：确保 MySQL 使用 utf8mb4 字符集
/**
 * API 路由智能切换器
 * 
 * 功能：
 * - 根据配置决定使用 Django API 还是 Next.js API
 * - 实现 Django API 健康检查
 * - 提供优雅降级机制
 * - 缓存健康检查结果以提高性能
 */

import { ApiConfig, HealthCheckResult, ApiRouteDecision, ApiRouterCache } from '@/types/api';

class ApiRouter {
  private config: ApiConfig;
  private cache: ApiRouterCache;

  constructor() {
    this.config = this.loadConfig();
    this.cache = {
      lastUpdated: 0
    };
  }

  /**
   * 从环境变量加载配置
   */
  private loadConfig(): ApiConfig {
    return {
      useDjangoApi: process.env.USE_DJANGO_API === 'true',
      djangoApiUrl: process.env.DJANGO_API_URL || 'http://localhost:5328',
      fallbackEnabled: process.env.API_FALLBACK_ENABLED !== 'false',
      healthCheckTimeout: parseInt(process.env.API_HEALTH_CHECK_TIMEOUT || '2000'),
      healthCheckCacheTtl: parseInt(process.env.API_HEALTH_CHECK_CACHE_TTL || '30000')
    };
  }

  /**
   * 决定 API 路由
   */
  async decideRoute(apiPath: string): Promise<ApiRouteDecision> {
    // 如果配置为不使用 Django API，直接使用 Next.js
    if (!this.config.useDjangoApi) {
      return {
        useDjango: false,
        reason: 'Django API disabled in configuration',
        isFallback: false
      };
    }

    // 检查 Django API 健康状态
    const healthCheck = await this.checkDjangoHealth();
    
    if (healthCheck.isHealthy) {
      const targetUrl = `${this.config.djangoApiUrl}/api${apiPath}`;
      return {
        useDjango: true,
        targetUrl,
        reason: 'Django API is healthy',
        isFallback: false
      };
    }

    // Django API 不健康，检查是否启用回退
    if (this.config.fallbackEnabled) {
      return {
        useDjango: false,
        reason: `Django API unhealthy, fallback to Next.js: ${healthCheck.error}`,
        isFallback: true
      };
    }

    // 不启用回退，仍然尝试使用 Django API
    const targetUrl = `${this.config.djangoApiUrl}/api${apiPath}`;
    return {
      useDjango: true,
      targetUrl,
      reason: 'Django API unhealthy but fallback disabled',
      isFallback: false
    };
  }

  /**
   * 检查 Django API 健康状态
   */
  private async checkDjangoHealth(): Promise<HealthCheckResult> {
    const now = Date.now();
    
    // 检查缓存是否有效
    if (this.cache.healthCheck && 
        (now - this.cache.healthCheck.timestamp) < this.config.healthCheckCacheTtl) {
      return this.cache.healthCheck;
    }

    const startTime = now;
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.healthCheckTimeout);

      const response = await fetch(`${this.config.djangoApiUrl}/api/hello`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      });

      clearTimeout(timeoutId);

      const responseTime = Date.now() - startTime;
      const isHealthy = response.ok;

      const result: HealthCheckResult = {
        isHealthy,
        timestamp: now,
        responseTime,
        error: isHealthy ? undefined : `HTTP ${response.status}: ${response.statusText}`
      };

      // 缓存结果
      this.cache.healthCheck = result;
      this.cache.lastUpdated = now;

      return result;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      const result: HealthCheckResult = {
        isHealthy: false,
        timestamp: now,
        responseTime,
        error: errorMessage
      };

      // 缓存失败结果（较短时间）
      this.cache.healthCheck = result;
      this.cache.lastUpdated = now;

      return result;
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): ApiConfig {
    return { ...this.config };
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(): ApiRouterCache {
    return { ...this.cache };
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache = {
      lastUpdated: 0
    };
  }

  /**
   * 记录路由决策日志
   */
  logDecision(decision: ApiRouteDecision, originalPath: string): void {
    const logLevel = decision.isFallback ? 'warn' : 'info';
    const message = `API Route Decision: ${originalPath} -> ${decision.useDjango ? 'Django' : 'Next.js'} (${decision.reason})`;
    
    if (logLevel === 'warn') {
      console.warn(`⚠️ ${message}`);
    } else {
      console.log(`ℹ️ ${message}`);
    }
  }
}

// 单例实例
export const apiRouter = new ApiRouter();

// 导出类型和实例
export { ApiRouter };
export type { ApiConfig, HealthCheckResult, ApiRouteDecision, ApiRouterCache };

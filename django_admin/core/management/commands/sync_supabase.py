from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.utils import timezone
from core.models import User, Topic, YuanbaoTransaction, Group
from core.supabase_client import SupabaseClient
import os
import json
from datetime import datetime
import re
import logging

logger = logging.getLogger(__name__)

def parse_datetime_to_naive(datetime_str):
    """
    将各种格式的时间字符串转换为naive datetime
    支持的格式：
    - 2024-08-12T10:30:00Z
    - 2024-08-12T10:30:00.123456+00:00
    - 2024-08-12T10:30:00+08:00
    """
    if not datetime_str:
        return None
    
    try:
        # 移除各种时区标识
        # 1. 移除Z结尾
        clean_str = datetime_str.replace('Z', '')
        
        # 2. 移除时区偏移 (+XX:XX, -XX:XX, +XXXX, -XXXX)
        clean_str = re.sub(r'[+-]\d{2}:?\d{2}$', '', clean_str)
        
        # 3. 确保没有时区信息后解析
        dt = datetime.fromisoformat(clean_str)
        
        # 4. 确保返回naive datetime
        if dt.tzinfo is not None:
            dt = dt.replace(tzinfo=None)
            
        return dt
        
    except Exception as e:
        logger.error(f"时间解析失败: {datetime_str} - {str(e)}")
        return None

class Command(BaseCommand):
    help = '从Supabase同步数据到Django数据库'

    def add_arguments(self, parser):
        parser.add_argument(
            '--data-file',
            type=str,
            help='指定数据文件路径（JSON格式）',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行模式，不实际保存数据',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制同步，覆盖现有数据',
        )

    def sync_single_user(self, user_data, dry_run=False):
        """同步单个用户"""
        try:
            # 获取User模型允许的字段
            allowed_fields = {f.name for f in User._meta.get_fields() if not f.is_relation}
            allowed_fields.discard('date_joined')
            allowed_fields.discard('id')
            
            # 过滤字段，只保留模型中存在的字段
            filtered_data = {k: v for k, v in user_data.items() if k in allowed_fields}
            
            # 转换时间格式
            if filtered_data.get('join_time'):
                filtered_data['join_time'] = parse_datetime_to_naive(filtered_data['join_time'])
            if filtered_data.get('expiration_time'):
                filtered_data['expiration_time'] = parse_datetime_to_naive(filtered_data['expiration_time'])
            
            if not dry_run:
                user, created = User.objects.get_or_create(
                    zsxq_group_member_id=filtered_data['zsxq_group_member_id'],
                    defaults=filtered_data
                )
                return user
            else:
                return None
                
        except Exception as e:
            logger.error(f"同步单个用户失败: {str(e)}")
            return None

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('开始Supabase数据同步...')
        )
        
        data_file = options.get('data_file')
        dry_run = options.get('dry_run')
        force = options.get('force')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('试运行模式：不会实际保存数据')
            )
        
        # 初始化Supabase客户端
        self.supabase_client = SupabaseClient()
        
        # 测试连接
        if not self.supabase_client.test_connection():
            self.stdout.write(
                self.style.WARNING('Supabase连接失败，将使用模拟数据')
            )
        
        try:
            # 1. 同步用户数据（必须先同步）
            user_stats = self.sync_users(data_file, dry_run, force)
            
            # 2. 同步元宝余额数据（依赖用户）
            balance_stats = self.sync_yuanbao_balances(data_file, dry_run, force)
            
            # 3. 同步帖子数据（依赖用户）
            topic_stats = self.sync_topics(data_file, dry_run, force)
            
            # 4. 同步元宝交易数据（依赖用户和余额）
            transaction_stats = self.sync_yuanbao_transactions(data_file, dry_run, force)
            
            self.stdout.write(
                self.style.SUCCESS('数据同步完成！')
            )
            
        except Exception as e:
            raise CommandError(f'数据同步失败: {str(e)}')

    def sync_users(self, data_file, dry_run, force):
        """同步用户数据"""
        self.stdout.write('正在同步用户数据...')
        
        # 从Supabase获取用户数据
        try:
            users_data = self.supabase_client.get_all_users()
            self.stdout.write(f'  从Supabase获取到 {len(users_data)} 个用户')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  获取Supabase用户数据失败: {str(e)}')
            )
            return
        
        # 获取User模型允许的字段
        allowed_fields = {f.name for f in User._meta.get_fields() if not f.is_relation}
        # 排除自动设置的字段
        allowed_fields.discard('date_joined')
        allowed_fields.discard('id')
        
        created_count = 0
        updated_count = 0
        
        for user_data in users_data:
            try:
                # 过滤字段，只保留模型中存在的字段
                filtered_data = {k: v for k, v in user_data.items() if k in allowed_fields}
                
                # 转换时间格式 - 使用强化的时间解析函数
                if filtered_data.get('join_time'):
                    # ✅ 修复：使用强化的时间解析函数
                    filtered_data['join_time'] = parse_datetime_to_naive(filtered_data['join_time'])
                if filtered_data.get('expiration_time'):
                    # ✅ 修复：使用强化的时间解析函数
                    filtered_data['expiration_time'] = parse_datetime_to_naive(filtered_data['expiration_time'])
                
                # 检查用户是否存在
                if not dry_run:
                    user, created = User.objects.get_or_create(
                        zsxq_group_member_id=filtered_data['zsxq_group_member_id'],
                        defaults=filtered_data
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(f'  创建用户: {user.name} (ID: {user.zsxq_group_member_id})')
                    else:
                        if force:
                            # 强制更新
                            for key, value in filtered_data.items():
                                if key != 'zsxq_group_member_id':  # 不更新主键字段
                                    setattr(user, key, value)
                            user.save()
                            updated_count += 1
                            self.stdout.write(f'  更新用户: {user.name} (ID: {user.zsxq_group_member_id})')
                        else:
                            self.stdout.write(f'  跳过用户: {user.name} (ID: {user.zsxq_group_member_id}) - 已存在')
                else:
                    # 试运行模式，只记录但不实际创建
                    created_count += 1
                    self.stdout.write(f'  [试运行] 将创建用户: {filtered_data.get("name", "未知")} (ID: {filtered_data["zsxq_group_member_id"]})')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  同步用户失败: {user_data.get("name", "未知")} - {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'用户同步完成: 创建 {created_count} 个，更新 {updated_count} 个')
        )

    def sync_topics(self, data_file, dry_run, force):
        """同步帖子数据"""
        self.stdout.write('正在同步帖子数据...')
        
        # 从Supabase获取帖子数据
        try:
            topics_data = self.supabase_client.get_all_topics()
            self.stdout.write(f'  从Supabase获取到 {len(topics_data)} 个帖子')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  获取Supabase帖子数据失败: {str(e)}')
            )
            return
        
        # 获取Topic模型允许的字段
        allowed_fields = {f.name for f in Topic._meta.get_fields() if not f.is_relation}
        allowed_fields.discard('id')
        
        created_count = 0
        updated_count = 0
        
        for topic_data in topics_data:
            try:
                # 过滤字段，只保留模型中存在的字段
                filtered_data = {k: v for k, v in topic_data.items() if k in allowed_fields}
                
                # 转换时间格式 - 使用强化的时间解析函数
                if filtered_data.get('create_time'):
                    # ✅ 修复：使用强化的时间解析函数
                    filtered_data['create_time'] = parse_datetime_to_naive(filtered_data['create_time'])
                if filtered_data.get('modify_time'):
                    # ✅ 修复：使用强化的时间解析函数
                    filtered_data['modify_time'] = parse_datetime_to_naive(filtered_data['modify_time'])
                
                # 处理group_id外键
                group_id = topic_data.get('group_id')
                if group_id:
                    group, created = Group.objects.get_or_create(
                        group_id=group_id,
                        defaults={'name': f'Group {group_id}'}
                    )
                    filtered_data['group'] = group
                
                # 获取关联的用户 - 增强版本，支持按需同步
                owner_id = topic_data.get('owner_id')
                if owner_id:
                    try:
                        # ✅ 修复：使用id字段查找，因为owner_id关联到users.id
                        owner = User.objects.get(id=owner_id)
                        filtered_data['owner'] = owner
                    except User.DoesNotExist:
                        # 🚀 新增：按需同步缺失的用户
                        self.stdout.write(
                            self.style.WARNING(f'  用户ID {owner_id} 不存在，尝试从Supabase获取...')
                        )
                        user_data = self.supabase_client.get_user_by_id(owner_id)
                        if user_data:
                            try:
                                # 同步这个用户
                                owner = self.sync_single_user(user_data, dry_run)
                                if owner:
                                    filtered_data['owner'] = owner
                                    self.stdout.write(
                                        self.style.SUCCESS(f'  ✅ 成功同步用户: {owner.name} (ID: {owner.id})')
                                    )
                                else:
                                    self.stdout.write(
                                        self.style.WARNING(f'  跳过帖子: 用户ID {owner_id} 同步失败')
                                    )
                                    continue
                            except Exception as e:
                                self.stdout.write(
                                    self.style.ERROR(f'  同步用户ID {owner_id} 失败: {str(e)}')
                                )
                                continue
                        else:
                            self.stdout.write(
                                self.style.WARNING(f'  跳过帖子: 用户ID {owner_id} 在Supabase中不存在')
                            )
                            continue
                
                # 检查帖子是否存在
                topic, created = Topic.objects.get_or_create(
                    topic_id=filtered_data['topic_id'],
                    defaults=filtered_data
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f'  创建帖子: {topic.title} (ID: {topic.topic_id})')
                else:
                    if force:
                        # 强制更新
                        for key, value in filtered_data.items():
                            if key != 'topic_id':  # 不更新主键字段
                                setattr(topic, key, value)
                        topic.save()
                        updated_count += 1
                        self.stdout.write(f'  更新帖子: {topic.title} (ID: {topic.topic_id})')
                    else:
                        self.stdout.write(f'  跳过帖子: {topic.title} (ID: {topic.topic_id}) - 已存在')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  同步帖子失败: {topic_data.get("title", "未知")} - {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'帖子同步完成: 创建 {created_count} 个，更新 {updated_count} 个')
        )

    def sync_yuanbao_transactions(self, data_file, dry_run, force):
        """同步元宝交易数据"""
        self.stdout.write('正在同步元宝交易数据...')
        
        # 从Supabase获取元宝交易数据
        try:
            transactions_data = self.supabase_client.get_all_yuanbao_transactions()
            self.stdout.write(f'  从Supabase获取到 {len(transactions_data)} 条元宝交易')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  获取Supabase元宝交易数据失败: {str(e)}')
            )
            return
        
        created_count = 0
        updated_count = 0
        
        for transaction_data in transactions_data:
            try:
                # 转换时间格式 - 使用强化的时间解析函数
                if transaction_data.get('created_at'):
                    # ✅ 修复：使用强化的时间解析函数，处理各种时区格式
                    transaction_data['created_at'] = parse_datetime_to_naive(transaction_data['created_at'])
                
                # 获取关联的用户
                user_id = transaction_data.get('user_id')
                if user_id:
                    try:
                        # ✅ 修复：使用id字段查找，因为user_id关联到users.id
                        user = User.objects.get(id=user_id)
                        transaction_data['user'] = user
                    except User.DoesNotExist:
                        self.stdout.write(
                            self.style.WARNING(f'  跳过交易: 用户ID {user_id} 不存在')
                        )
                        continue
                
                # 处理created_by字段
                created_by_id = transaction_data.get('created_by')
                if created_by_id:
                    try:
                        # ✅ 修复：直接使用created_by ID查找，不再使用created_by_name
                        created_by_user = User.objects.get(id=created_by_id)
                        transaction_data['created_by'] = created_by_user
                    except User.DoesNotExist:
                        self.stdout.write(
                            self.style.WARNING(f'  操作人ID {created_by_id} 不存在，设为空')
                        )
                        transaction_data['created_by'] = None
                else:
                    transaction_data['created_by'] = None

                # 过滤掉模型没有的字段
                allowed_fields = {f.name for f in YuanbaoTransaction._meta.get_fields() if not f.is_relation}
                allowed_fields.update(['user', 'created_by'])  # 添加外键字段
                allowed_fields.discard('id')
                
                # 移除不需要的字段
                filtered_data = {k: v for k, v in transaction_data.items() if k in allowed_fields}
                
                # 检查交易是否存在（基于用户、描述和时间）
                existing_transaction = YuanbaoTransaction.objects.filter(
                    user=filtered_data['user'],
                    description=filtered_data.get('description', ''),
                    created_at=filtered_data['created_at']
                ).first()
                
                if existing_transaction:
                    if force:
                        # 强制更新
                        for key, value in filtered_data.items():
                            if key not in ['user', 'created_at']:  # 不更新用户和创建时间
                                setattr(existing_transaction, key, value)
                        existing_transaction.save()
                        updated_count += 1
                        self.stdout.write(f'  更新交易: {existing_transaction.description} (用户: {existing_transaction.user.name})')
                    else:
                        self.stdout.write(f'  跳过交易: {filtered_data.get("description", "未知")} (用户: {user.name}) - 已存在')
                else:
                    # 创建新交易
                    if not dry_run:
                        transaction = YuanbaoTransaction(**filtered_data)
                        transaction.save()
                    created_count += 1
                    self.stdout.write(f'  创建交易: {filtered_data.get("description", "未知")} (用户: {user.name})')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  同步交易失败: {transaction_data.get("description", "未知")} - {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'元宝交易同步完成: 创建 {created_count} 个，更新 {updated_count} 个')
        ) 